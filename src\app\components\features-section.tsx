import { Truck, Lock, RotateCcw, Headphones } from "lucide-react"

export function FeaturesSection() {
  const features = [
    {
      icon: Truck,
      title: "Free Shipping",
      description: "Order over $100",
    },
    {
      icon: Lock,
      title: "Secure Payment",
      description: "100% secure payment",
    },
    {
      icon: RotateCcw,
      title: "Easy Returns",
      description: "20 days return",
    },
    {
      icon: Headphones,
      title: "24/7 Support",
      description: "Call us anytime",
    },
  ]

  return (
    <section className="py-16 px-4 sm:px-8">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-6 p-4">
              <feature.icon className="h-14 w-14 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
