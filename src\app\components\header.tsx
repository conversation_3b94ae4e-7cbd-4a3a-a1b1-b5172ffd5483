"use client"

import { useState, useEffect } from "react"
import { Search, ShoppingCart, Heart, User, Home, List, Tags, MessageCircle, BookOpen } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import Link from "next/link"
import { useAuth } from "@/hooks/useAuth"

interface HeaderProps {
  onLoginClick: () => void
}

export function Header({ onLoginClick }: HeaderProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { user, logout } = useAuth()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 80)
      setIsSearchOpen(false)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navigationItems = [
    { label: "home", href: "#home" },
    { label: "featured", href: "#featured" },
    { label: "arrivals", href: "#arrivals" },
    { label: "reviews", href: "#reviews" },
    { label: "blogs", href: "#blogs" },
  ]

  return (
    <>
      <header className="header">
        {/* Header 1 */}
        <div className="bg-white px-4 sm:px-8 py-6 flex items-center justify-between shadow-sm relative">
          <Link href="#" className="flex items-center gap-2 text-2xl font-bold text-gray-800">
            <BookOpen className="h-7 w-7 text-blue-600" />
            book hunt
          </Link>

          {/* Desktop Search Form */}
          <form className="hidden md:flex items-center w-full max-w-lg mx-8 h-12 border border-gray-300 rounded-lg overflow-hidden bg-white">
            <Input
              type="search"
              placeholder="search here..."
              className="flex-1 h-full border-none text-base px-4 focus:ring-0"
            />
            <Button type="submit" variant="ghost" size="sm" className="h-full px-6 hover:text-blue-600">
              <Search className="h-6 w-6" />
            </Button>
          </form>

          {/* Mobile Search Form */}
          {isSearchOpen && (
            <form className="absolute top-full right-8 left-8 mt-4 md:hidden flex items-center h-12 border border-gray-300 rounded-lg overflow-hidden bg-white shadow-lg z-50">
              <Input
                type="search"
                placeholder="search here..."
                className="flex-1 h-full border-none text-base px-4 focus:ring-0"
              />
              <Button type="submit" variant="ghost" size="sm" className="h-full px-6 hover:text-blue-600">
                <Search className="h-6 w-6" />
              </Button>
            </form>
          )}

          {/* Icons */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden text-gray-800 hover:text-blue-600"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
            >
              <Search className="h-6 w-6" />
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-800 hover:text-blue-600">
              <ShoppingCart className="h-6 w-6" />
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-800 hover:text-blue-600">
              <Heart className="h-6 w-6" />
            </Button>

            {/* User Menu */}
            <div className="flex items-center gap-2">
              <span className="hidden sm:inline text-sm text-gray-600">Welcome, {user?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-800 hover:text-blue-600"
                onClick={logout}
                title="Logout"
              >
                <User className="h-6 w-6" />
              </Button>
            </div>
          </div>
        </div>

        {/* Header 2 - Desktop Navigation */}
        <div
          className={`bg-blue-600 transition-all duration-300 hidden md:block ${isScrolled ? "fixed top-0 left-0 right-0 z-50" : ""}`}
        >
          <nav className="text-center">
            {navigationItems.map((item) => (
              <Link
                key={item.label}
                href={item.href}
                className="inline-block text-white text-lg px-4 py-3 hover:bg-blue-800 transition-colors capitalize"
              >
                {item.label}
              </Link>
            ))}
          </nav>
        </div>
      </header>

      {/* Bottom Navigation - Mobile */}
      <nav className="fixed bottom-0 left-0 right-0 bg-blue-600 z-50 md:hidden">
        <div className="flex justify-around py-4">
          <Link href="#home" className="text-white hover:text-blue-200">
            <Home className="h-6 w-6" />
          </Link>
          <Link href="#featured" className="text-white hover:text-blue-200">
            <List className="h-6 w-6" />
          </Link>
          <Link href="#arrivals" className="text-white hover:text-blue-200">
            <Tags className="h-6 w-6" />
          </Link>
          <Link href="#reviews" className="text-white hover:text-blue-200">
            <MessageCircle className="h-6 w-6" />
          </Link>
          <Link href="#blogs" className="text-white hover:text-blue-200">
            <BookOpen className="h-6 w-6" />
          </Link>
        </div>
      </nav>
    </>
  )
}
