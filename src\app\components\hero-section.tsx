"use client"

import { useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export function HeroSection() {
  const sliderRef = useRef<HTMLDivElement>(null)

  const books = [
    "/placeholder.svg?height=400&width=300&text=Book+1",
    "/placeholder.svg?height=400&width=300&text=Book+2",
    "/placeholder.svg?height=400&width=300&text=Book+3",
    "/placeholder.svg?height=400&width=300&text=Book+4",
    "/placeholder.svg?height=400&width=300&text=Book+5",
    "/placeholder.svg?height=400&width=300&text=Book+6",
  ]

  useEffect(() => {
    const slider = sliderRef.current
    if (!slider) return

    let currentIndex = 0
    const slideCount = books.length
    const slideWidth = 300 // Approximate width of each book

    const autoSlide = () => {
      currentIndex = (currentIndex + 1) % slideCount
      const translateX = -currentIndex * slideWidth
      slider.style.transform = `translateX(${translateX}px)`
    }

    const interval = setInterval(autoSlide, 3000)
    return () => clearInterval(interval)
  }, [books.length])

  return (
    <section
      id="home"
      className="min-h-screen bg-cover bg-center bg-no-repeat flex items-center"
      style={{
        backgroundImage: "url('/placeholder.svg?height=800&width=1200&text=Banner+Background')",
      }}
    >
      <div className="container mx-auto px-4 sm:px-8 py-16">
        <div className="flex flex-col lg:flex-row items-center gap-8">
          {/* Content */}
          <div className="flex-1 text-center lg:text-left">
            <h3 className="text-4xl lg:text-6xl font-bold text-gray-800 mb-4">up to 60% off</h3>
            <p className="text-gray-600 text-lg leading-relaxed mb-8 max-w-2xl">
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Et mollitia aut ratione, repellat repudiandae
              natus nulla adipisci ea debitis architecto eaque dignissimos totam ad ipsa quis optio error quod
              voluptatem.
            </p>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-800 text-white px-8 py-3 text-lg">
              Shop Now
            </Button>
          </div>

          {/* Book Slider */}
          <div className="flex-1 relative">
            <div className="overflow-hidden max-w-2xl mx-auto">
              <div
                ref={sliderRef}
                className="flex transition-transform duration-500 ease-in-out"
                style={{ width: `${books.length * 300}px` }}
              >
                {books.map((book, index) => (
                  <div key={index} className="flex-shrink-0 w-72 px-4">
                    <div className="relative group cursor-pointer">
                      <Image
                        src={book || "/placeholder.svg"}
                        alt={`Book ${index + 1}`}
                        width={250}
                        height={350}
                        className="rounded-lg shadow-lg transition-transform duration-300 group-hover:scale-95 group-hover:-translate-y-4"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-8 text-center">
              <Image
                src="/placeholder.svg?height=100&width=400&text=Book+Stand"
                alt="Book Stand"
                width={400}
                height={100}
                className="mx-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
