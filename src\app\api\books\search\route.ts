import { type NextRequest, NextResponse } from "next/server"
import { searchBooks } from "@/lib/google-books"

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const query = searchParams.get("q")
  const startIndex = Number.parseInt(searchParams.get("startIndex") || "0")
  const maxResults = Number.parseInt(searchParams.get("maxResults") || "20")

  if (!query) {
    return NextResponse.json({ error: "Query parameter is required" }, { status: 400 })
  }

  try {
    const data = await searchBooks(query, startIndex, maxResults)

    return NextResponse.json({
      books: data.items || [],
      totalItems: data.totalItems || 0,
    })
  } catch (error) {
    console.error("Error searching books:", error)
    return NextResponse.json({ error: "Failed to search books" }, { status: 500 })
  }
}
