"use client"

import { useState, useEffect, create<PERSON>ontext, useContext } from "react"
import type { User, AuthState, LoginCredentials, SignUpCredentials } from "@/types/auth"

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>
  signUp: (credentials: SignUpCredentials) => Promise<void>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export { AuthContext }

// Mock authentication functions - replace with real API calls
const mockLogin = async (credentials: LoginCredentials): Promise<User> => {
  await new Promise((resolve) => setTimeout(resolve, 2000)) // Simulate API delay

  if (credentials.email === "<EMAIL>" && credentials.password === "password") {
    return {
      id: "1",
      email: credentials.email,
      name: "Demo User",
      createdAt: new Date(),
    }
  }
  throw new Error("Invalid credentials")
}

const mockSignUp = async (credentials: SignUpCredentials): Promise<User> => {
  await new Promise((resolve) => setTimeout(resolve, 2000)) // Simulate API delay

  if (credentials.password !== credentials.confirmPassword) {
    throw new Error("Passwords do not match")
  }

  return {
    id: Math.random().toString(36).substr(2, 9),
    email: credentials.email,
    name: credentials.name,
    createdAt: new Date(),
  }
}

export const useAuthLogic = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  })

  useEffect(() => {
    // Check for existing session on mount
    const checkExistingSession = async () => {
      try {
        // Simulate checking for existing session
        await new Promise((resolve) => setTimeout(resolve, 1500))

        const savedUser = localStorage.getItem("user")
        if (savedUser) {
          const user = JSON.parse(savedUser)
          setAuthState({
            user,
            isLoading: false,
            isAuthenticated: true,
          })
        } else {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          })
        }
      } catch (error) {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        })
      }
    }

    checkExistingSession()
  }, [])

  const login = async (credentials: LoginCredentials) => {
    setAuthState((prev) => ({ ...prev, isLoading: true }))

    try {
      const user = await mockLogin(credentials)
      localStorage.setItem("user", JSON.stringify(user))
      setAuthState({
        user,
        isLoading: false,
        isAuthenticated: true,
      })
    } catch (error) {
      setAuthState((prev) => ({ ...prev, isLoading: false }))
      throw error
    }
  }

  const signUp = async (credentials: SignUpCredentials) => {
    setAuthState((prev) => ({ ...prev, isLoading: true }))

    try {
      const user = await mockSignUp(credentials)
      localStorage.setItem("user", JSON.stringify(user))
      setAuthState({
        user,
        isLoading: false,
        isAuthenticated: true,
      })
    } catch (error) {
      setAuthState((prev) => ({ ...prev, isLoading: false }))
      throw error
    }
  }

  const logout = () => {
    localStorage.removeItem("user")
    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
    })
  }

  return {
    ...authState,
    login,
    signUp,
    logout,
  }
}
