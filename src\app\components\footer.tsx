import { MapPin, ArrowRight, Phone, Mail, MapIcon as MapMarkerIcon } from "lucide-react"
import Link from "next/link"

export function Footer() {
  const locations = ["India", "USA", "Russia", "France", "Japan", "Africa"]
  const quickLinks = ["Home", "Featured", "Arrivals", "Reviews", "Blogs"]
  const extraLinks = ["Account Info", "Ordered Items", "Privacy Policy", "Payment Method", "Our Services"]

  return (
    <footer className="bg-gray-100 py-16">
      <div className="container mx-auto px-4 sm:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* Locations */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Our Locations</h3>
            <div className="space-y-3">
              {locations.map((location) => (
                <Link
                  key={location}
                  href="#"
                  className="flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <MapPin className="h-4 w-4" />
                  {location}
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Quick Links</h3>
            <div className="space-y-3">
              {quickLinks.map((link) => (
                <Link
                  key={link}
                  href={`#${link.toLowerCase()}`}
                  className="flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors capitalize"
                >
                  <ArrowRight className="h-4 w-4" />
                  {link}
                </Link>
              ))}
            </div>
          </div>

          {/* Extra Links */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Extra Links</h3>
            <div className="space-y-3">
              {extraLinks.map((link) => (
                <Link
                  key={link}
                  href="#"
                  className="flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <ArrowRight className="h-4 w-4" />
                  {link}
                </Link>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Contact Info</h3>
            <div className="space-y-3">
              <Link href="tel:+************" className="flex items-center gap-2 text-gray-600 hover:text-blue-600">
                <Phone className="h-4 w-4" />
                +************
              </Link>
              <Link href="tel:+************" className="flex items-center gap-2 text-gray-600 hover:text-blue-600">
                <Phone className="h-4 w-4" />
                +************
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-gray-600 hover:text-blue-600"
              >
                <Mail className="h-4 w-4" />
                <EMAIL>
              </Link>
              <div className="flex items-center gap-2 text-gray-600">
                <MapMarkerIcon className="h-4 w-4" />
                Mumbai, India - 400104
              </div>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="text-center py-4 border-t border-gray-300">
          <div className="flex justify-center gap-4 mb-4">
            <Link
              href="https://www.facebook.com/bookhunt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center hover:bg-blue-800 transition-colors duration-300 transform hover:scale-110"
              aria-label="Follow us on Facebook"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
            </Link>

            <Link
              href="https://twitter.com/bookhunt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 rounded-full bg-blue-400 text-white flex items-center justify-center hover:bg-blue-600 transition-colors duration-300 transform hover:scale-110"
              aria-label="Follow us on Twitter"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
              </svg>
            </Link>

            <Link
              href="https://www.instagram.com/bookhunt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white flex items-center justify-center hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-110"
              aria-label="Follow us on Instagram"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.405c-.49 0-.928-.175-1.297-.49-.368-.315-.49-.753-.49-1.243 0-.49.122-.928.49-1.243.369-.315.807-.49 1.297-.49s.928.175 1.297.49c.315.315.49.753.49 1.243 0 .49-.175.928-.49 1.243-.369.315-.807.49-1.297.49zm-4.282 9.405c-2.448 0-4.474-2.026-4.474-4.474s2.026-4.474 4.474-4.474 4.474 2.026 4.474 4.474-2.026 4.474-4.474 4.474z" />
              </svg>
            </Link>

            <Link
              href="https://www.linkedin.com/company/bookhunt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 rounded-full bg-blue-700 text-white flex items-center justify-center hover:bg-blue-900 transition-colors duration-300 transform hover:scale-110"
              aria-label="Follow us on LinkedIn"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
              </svg>
            </Link>

            <Link
              href="https://www.pinterest.com/bookhunt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 rounded-full bg-red-600 text-white flex items-center justify-center hover:bg-red-800 transition-colors duration-300 transform hover:scale-110"
              aria-label="Follow us on Pinterest"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.347-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624.001 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z" />
              </svg>
            </Link>
          </div>
          <p className="text-gray-600 text-lg">
            Created by <span className="text-blue-600 font-semibold">Miss. Web Designer</span> | All rights reserved!
          </p>
        </div>
      </div>
    </footer>
  )
}
