"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Search, Heart, Eye } from "lucide-react"
import Image from "next/image"
import type { Book } from "@/types"

interface BookSliderProps {
  title: string
  books: Book[]
  showActions?: boolean
}

export function BookSlider({ title, books, showActions = false }: BookSliderProps) {
  const sliderRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextSlide = () => {
    if (sliderRef.current) {
      const maxIndex = books.length - 1
      const newIndex = currentIndex >= maxIndex ? 0 : currentIndex + 1
      setCurrentIndex(newIndex)
    }
  }

  const prevSlide = () => {
    if (sliderRef.current) {
      const newIndex = currentIndex <= 0 ? books.length - 1 : currentIndex - 1
      setCurrentIndex(newIndex)
    }
  }

  useEffect(() => {
    const interval = setInterval(nextSlide, 4000)
    return () => clearInterval(interval)
  }, [currentIndex])

  useEffect(() => {
    if (sliderRef.current) {
      const slideWidth = 280 // Width of each slide including gap
      sliderRef.current.style.transform = `translateX(-${currentIndex * slideWidth}px)`
    }
  }, [currentIndex])

  return (
    <section className="py-16 px-4 sm:px-8">
      <div className="container mx-auto">
        {/* Section Heading */}
        <div className="relative text-center mb-12">
          <div className="absolute top-1/2 left-0 right-0 h-px bg-gray-200 -translate-y-1/2 -z-10"></div>
          <h2 className="inline-block text-3xl font-bold text-gray-800 bg-white px-8 py-4 border border-gray-300 capitalize">
            {title}
          </h2>
        </div>

        {/* Slider Container */}
        <div className="relative">
          <div className="overflow-hidden">
            <div
              ref={sliderRef}
              className="flex transition-transform duration-500 ease-in-out gap-6"
              style={{ width: `${books.length * 280}px` }}
            >
              {books.map((book) => (
                <div key={book.id} className="flex-shrink-0 w-64">
                  <div className="group relative bg-white border border-gray-200 hover:border-gray-400 transition-all duration-300 overflow-hidden">
                    {/* Book Actions */}
                    {showActions && (
                      <div className="absolute top-0 left-0 right-0 bg-white border-b border-gray-400 z-10 flex transform -translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                        <Button variant="ghost" size="sm" className="flex-1 hover:bg-blue-600 hover:text-white">
                          <Search className="h-5 w-5" />
                        </Button>
                        <Button variant="ghost" size="sm" className="flex-1 hover:bg-blue-600 hover:text-white">
                          <Heart className="h-5 w-5" />
                        </Button>
                        <Button variant="ghost" size="sm" className="flex-1 hover:bg-blue-600 hover:text-white">
                          <Eye className="h-5 w-5" />
                        </Button>
                      </div>
                    )}

                    {/* Book Image */}
                    <div className="p-4 bg-gradient-to-br from-gray-100 to-white group-hover:translate-y-24 transition-transform duration-300">
                      <Image
                        src={book.image || "/placeholder.svg"}
                        alt={book.title}
                        width={200}
                        height={280}
                        className="w-full h-64 object-contain"
                      />
                    </div>

                    {/* Book Content */}
                    <div className="p-6 bg-gray-100 text-center">
                      <h3 className="text-xl font-semibold text-gray-800 mb-4">{book.title}</h3>
                      <div className="text-xl text-gray-600 mb-4">
                        ${book.price.toFixed(2)}
                        {book.originalPrice && (
                          <span className="ml-2 text-sm text-gray-400 line-through">
                            ${book.originalPrice.toFixed(2)}
                          </span>
                        )}
                      </div>
                      {showActions && <Button className="bg-blue-600 hover:bg-blue-800 text-white">Add to Cart</Button>}
                      {book.rating && (
                        <div className="flex justify-center mt-2">
                          {[...Array(5)].map((_, i) => (
                            <span
                              key={i}
                              className={`text-yellow-400 ${i < Math.floor(book.rating!) ? "fas" : i < book.rating! ? "fas fa-star-half-alt" : "far"} fa-star`}
                            >
                              ★
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Buttons */}
          {showActions && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="absolute top-1/2 -translate-y-1/2 left-4 w-12 h-12 bg-white border-gray-400 hover:bg-gray-800 hover:text-white"
                onClick={prevSlide}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="absolute top-1/2 -translate-y-1/2 right-4 w-12 h-12 bg-white border-gray-400 hover:bg-gray-800 hover:text-white"
                onClick={nextSlide}
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </>
          )}
        </div>
      </div>
    </section>
  )
}
