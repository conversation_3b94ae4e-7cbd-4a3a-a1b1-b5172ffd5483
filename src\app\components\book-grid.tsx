"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ExternalLink, Calendar, Users } from "lucide-react"
import type { GoogleBook } from "@/lib/google-books"
import { BookDetailsModal } from "./book-details-modal"

interface BookGridProps {
  books: GoogleBook[]
  onLoadMore?: () => void
  hasMore?: boolean
  isLoading?: boolean
}

export function BookGrid({ books, onLoadMore, hasMore = false, isLoading = false }: BookGridProps) {
  const [selectedBookId, setSelectedBookId] = useState<string | null>(null)

  if (books.length === 0 && !isLoading) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">No books found</div>
        <p className="text-gray-400">Try searching with different keywords</p>
      </div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {books.map((book) => (
          <Card key={book.id} className="h-full flex flex-col hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-4 flex-1">
              <div className="flex gap-4 h-full">
                {/* Book Cover */}
                <div className="flex-shrink-0">
                  <div className="w-20 h-28 relative bg-gray-100 rounded-md overflow-hidden">
                    {book.volumeInfo.imageLinks?.thumbnail ? (
                      <Image
                        src={book.volumeInfo.imageLinks.thumbnail.replace("http:", "https:") || "/placeholder.svg"}
                        alt={book.volumeInfo.title}
                        fill
                        className="object-cover"
                        sizes="80px"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center p-1">
                        No Cover
                      </div>
                    )}
                  </div>
                </div>

                {/* Book Details */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-base leading-tight mb-2 line-clamp-2">{book.volumeInfo.title}</h3>

                  {book.volumeInfo.authors && book.volumeInfo.authors.length > 0 && (
                    <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                      <Users className="h-3 w-3" />
                      <span className="truncate">{book.volumeInfo.authors[0]}</span>
                    </div>
                  )}

                  {book.volumeInfo.publishedDate && (
                    <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(book.volumeInfo.publishedDate).getFullYear()}</span>
                    </div>
                  )}

                  {book.volumeInfo.averageRating && (
                    <div className="flex items-center gap-1 mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{book.volumeInfo.averageRating}</span>
                      {book.volumeInfo.ratingsCount && (
                        <span className="text-xs text-gray-500">({book.volumeInfo.ratingsCount})</span>
                      )}
                    </div>
                  )}

                  {book.volumeInfo.categories && book.volumeInfo.categories.length > 0 && (
                    <div className="mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {book.volumeInfo.categories[0]}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>

            <CardFooter className="p-4 pt-0 flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setSelectedBookId(book.id)} className="flex-1">
                View Details
              </Button>
              {book.volumeInfo.previewLink && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => window.open(book.volumeInfo.previewLink, "_blank")}
                  className="flex-1 bg-blue-600 hover:bg-blue-800"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Preview
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      {hasMore && (
        <div className="text-center mt-8">
          <Button onClick={onLoadMore} disabled={isLoading} size="lg" variant="outline" className="px-8 bg-transparent">
            {isLoading ? "Loading..." : "Load More Books"}
          </Button>
        </div>
      )}

      <BookDetailsModal bookId={selectedBookId} isOpen={!!selectedBookId} onClose={() => setSelectedBookId(null)} />
    </>
  )
}
