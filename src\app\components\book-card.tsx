"use client"

import type { Book } from "@/types/book"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Star, ExternalLink, Calendar, Users } from "lucide-react"
import Image from "next/image"

interface BookCardProps {
  book: Book
  onViewDetails: (book: Book) => void
}

export function BookCard({ book, onViewDetails }: BookCardProps) {
  const {
    title,
    authors,
    description,
    publishedDate,
    imageLinks,
    averageRating,
    ratingsCount,
    categories,
    previewLink,
  } = book

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text
  }

  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-300">
      <CardContent className="p-4 flex-1">
        <div className="flex gap-4 h-full">
          {/* Book Cover */}
          <div className="flex-shrink-0">
            <div className="w-24 h-32 relative bg-gray-100 rounded-md overflow-hidden">
              {imageLinks?.thumbnail ? (
                <Image
                  src={imageLinks.thumbnail.replace("http:", "https:") || "/placeholder.svg"}
                  alt={title}
                  fill
                  className="object-cover"
                  sizes="96px"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center p-2">
                  No Cover Available
                </div>
              )}
            </div>
          </div>

          {/* Book Details */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg leading-tight mb-2 line-clamp-2">{title}</h3>

            {authors && authors.length > 0 && (
              <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                <Users className="h-3 w-3" />
                <span className="truncate">{authors.join(", ")}</span>
              </div>
            )}

            {publishedDate && (
              <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                <Calendar className="h-3 w-3" />
                <span>{new Date(publishedDate).getFullYear()}</span>
              </div>
            )}

            {averageRating && (
              <div className="flex items-center gap-1 mb-2">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{averageRating}</span>
                {ratingsCount && <span className="text-xs text-gray-500">({ratingsCount})</span>}
              </div>
            )}

            {description && <p className="text-sm text-gray-700 mb-3 line-clamp-3">{truncateText(description, 150)}</p>}

            {categories && categories.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {categories.slice(0, 2).map((category, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {category}
                  </Badge>
                ))}
                {categories.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{categories.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex gap-2">
        <Button variant="outline" size="sm" onClick={() => onViewDetails(book)} className="flex-1">
          View Details
        </Button>
        {previewLink && (
          <Button variant="default" size="sm" onClick={() => window.open(previewLink, "_blank")} className="flex-1">
            <ExternalLink className="h-3 w-3 mr-1" />
            Preview
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
