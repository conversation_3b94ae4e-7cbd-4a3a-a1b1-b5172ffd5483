import { Button } from "@/components/ui/button"
import Image from "next/image"

export function DealSection() {
  return (
    <section className="py-16 bg-gray-100">
      <div className="container mx-auto px-4 sm:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-8">
          {/* Content */}
          <div className="flex-1 text-center lg:text-left">
            <h3 className="text-2xl text-blue-600 font-semibold mb-2">Deal of the day</h3>
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-4">up to 50% off</h1>
            <p className="text-gray-600 text-lg leading-relaxed mb-8 max-w-2xl">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis, libero necessitatibus similique quasi
              dolores nemo molestiae a, tenetur non, vero eligendi.
            </p>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-800 text-white px-8 py-3 text-lg">
              Shop Now
            </Button>
          </div>

          {/* Image */}
          <div className="flex-1">
            <Image
              src="/placeholder.svg?height=400&width=500&text=Deal+Book"
              alt="Deal Book"
              width={500}
              height={400}
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
