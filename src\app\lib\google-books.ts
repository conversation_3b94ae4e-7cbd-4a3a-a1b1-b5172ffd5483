export interface GoogleBook {
  id: string
  volumeInfo: {
    title: string
    authors?: string[]
    imageLinks?: {
      thumbnail: string
      smallThumbnail?: string
    }
    description?: string
    averageRating?: number
    ratingsCount?: number
    pageCount?: number
    publishedDate?: string
    publisher?: string
    previewLink?: string
    infoLink?: string
    categories?: string[]
    language?: string
  }
}

export interface GoogleBooksResponse {
  items?: GoogleBook[]
  totalItems: number
}

const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_BOOKS_API_KEY || "AIzaSyBdYA757m-2SvKM7Ry_4yhC8cZD3e50deE"

export async function searchBooks(query: string, startIndex = 0, maxResults = 20): Promise<GoogleBooksResponse> {
  try {
    const response = await fetch(
      `https://www.googleapis.com/books/v1/volumes?q=${encodeURIComponent(query)}&startIndex=${startIndex}&maxResults=${maxResults}&key=${API_KEY}`,
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching books:", error)
    return { totalItems: 0, items: [] }
  }
}

export async function getBookById(id: string): Promise<GoogleBook | null> {
  try {
    const response = await fetch(`https://www.googleapis.com/books/v1/volumes/${id}?key=${API_KEY}`)

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching book:", error)
    return null
  }
}

export async function getTrendingBooks(): Promise<GoogleBook[]> {
  try {
    const response = await searchBooks("bestseller fiction", 0, 40)
    return response.items || []
  } catch (error) {
    console.error("Error fetching trending books:", error)
    return []
  }
}

export async function getFeaturedBooks(): Promise<GoogleBook[]> {
  try {
    const response = await searchBooks("popular books 2024", 0, 20)
    return response.items || []
  } catch (error) {
    console.error("Error fetching featured books:", error)
    return []
  }
}

export async function getNewArrivals(): Promise<GoogleBook[]> {
  try {
    const response = await searchBooks("new releases 2024", 0, 20)
    return response.items || []
  } catch (error) {
    console.error("Error fetching new arrivals:", error)
    return []
  }
}

// Convert GoogleBook to our Book interface
export function convertGoogleBookToBook(googleBook: GoogleBook) {
  return {
    id: googleBook.id,
    title: googleBook.volumeInfo.title || "Unknown Title",
    authors: googleBook.volumeInfo.authors,
    description: googleBook.volumeInfo.description,
    publishedDate: googleBook.volumeInfo.publishedDate,
    pageCount: googleBook.volumeInfo.pageCount,
    categories: googleBook.volumeInfo.categories,
    imageLinks: googleBook.volumeInfo.imageLinks,
    previewLink: googleBook.volumeInfo.previewLink,
    infoLink: googleBook.volumeInfo.infoLink,
    averageRating: googleBook.volumeInfo.averageRating,
    ratingsCount: googleBook.volumeInfo.ratingsCount,
    publisher: googleBook.volumeInfo.publisher,
    language: googleBook.volumeInfo.language,
    price: Math.floor(Math.random() * 20) + 5, // Mock price since Google Books doesn't provide pricing
    originalPrice: Math.floor(Math.random() * 30) + 15,
    image:
      googleBook.volumeInfo.imageLinks?.thumbnail?.replace("http:", "https:") ||
      "/placeholder.svg?height=350&width=250&text=No+Cover",
  }
}
