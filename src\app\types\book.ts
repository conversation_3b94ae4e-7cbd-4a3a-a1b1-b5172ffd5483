export interface Book {
  id: string
  title: string
  authors?: string[]
  description?: string
  publishedDate?: string
  pageCount?: number
  categories?: string[]
  imageLinks?: {
    thumbnail?: string
    smallThumbnail?: string
  }
  previewLink?: string
  infoLink?: string
  averageRating?: number
  ratingsCount?: number
  publisher?: string
  language?: string
}

export interface BookSearchResponse {
  items?: Array<{
    id: string
    volumeInfo: Omit<Book, "id">
  }>
  totalItems: number
}
