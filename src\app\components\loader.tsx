"use client"

import { useEffect, useState } from "react"
import Image from "next/image"

export function Loader() {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-white via-gray-50 to-gray-200 flex items-center justify-center backdrop-blur-sm">
      <Image
        src="/placeholder.svg?height=160&width=160&text=Loading..."
        alt="Loading"
        width={160}
        height={160}
        className="animate-pulse"
      />
    </div>
  )
}
