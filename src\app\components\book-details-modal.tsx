"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Star, ExternalLink, Calendar, Users, BookOpen, Globe, Building, Loader2 } from "lucide-react"
import Image from "next/image"
import type { GoogleBook } from "@/lib/google-books"

interface BookDetailsModalProps {
  bookId: string | null
  isOpen: boolean
  onClose: () => void
}

export function BookDetailsModal({ bookId, isOpen, onClose }: BookDetailsModalProps) {
  const [book, setBook] = useState<GoogleBook | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (bookId && isOpen) {
      fetchBookDetails(bookId)
    }
  }, [bookId, isOpen])

  const fetchBookDetails = async (id: string) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/books/${id}`)
      if (!response.ok) {
        throw new Error("Failed to fetch book details")
      }
      const bookData = await response.json()
      setBook(bookData)
    } catch (err) {
      setError("Failed to load book details")
      console.error("Error fetching book details:", err)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading book details...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        ) : book ? (
          <>
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold pr-8">{book.volumeInfo.title}</DialogTitle>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Book Cover */}
              <div className="md:col-span-1">
                <div className="w-full max-w-64 mx-auto aspect-[3/4] relative bg-gray-100 rounded-lg overflow-hidden">
                  {book.volumeInfo.imageLinks?.thumbnail ? (
                    <Image
                      src={book.volumeInfo.imageLinks.thumbnail.replace("http:", "https:") || "/placeholder.svg"}
                      alt={book.volumeInfo.title}
                      fill
                      className="object-cover"
                      sizes="256px"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      No Cover Available
                    </div>
                  )}
                </div>
              </div>

              {/* Book Information */}
              <div className="md:col-span-2 space-y-4">
                {/* Authors */}
                {book.volumeInfo.authors && book.volumeInfo.authors.length > 0 && (
                  <div className="flex items-start gap-2">
                    <Users className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-700">Authors</p>
                      <p className="text-gray-600">{book.volumeInfo.authors.join(", ")}</p>
                    </div>
                  </div>
                )}

                {/* Rating */}
                {book.volumeInfo.averageRating && (
                  <div className="flex items-center gap-2">
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{book.volumeInfo.averageRating}</span>
                    {book.volumeInfo.ratingsCount && (
                      <span className="text-gray-500">({book.volumeInfo.ratingsCount} ratings)</span>
                    )}
                  </div>
                )}

                {/* Publication Details */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {book.volumeInfo.publishedDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        Published: {new Date(book.volumeInfo.publishedDate).getFullYear()}
                      </span>
                    </div>
                  )}

                  {book.volumeInfo.pageCount && (
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{book.volumeInfo.pageCount} pages</span>
                    </div>
                  )}

                  {book.volumeInfo.publisher && (
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{book.volumeInfo.publisher}</span>
                    </div>
                  )}

                  {book.volumeInfo.language && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Language: {book.volumeInfo.language}</span>
                    </div>
                  )}
                </div>

                {/* Categories */}
                {book.volumeInfo.categories && book.volumeInfo.categories.length > 0 && (
                  <div>
                    <p className="font-medium text-gray-700 mb-2">Categories</p>
                    <div className="flex flex-wrap gap-2">
                      {book.volumeInfo.categories.map((category, index) => (
                        <Badge key={index} variant="secondary">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Description */}
                {book.volumeInfo.description && (
                  <div>
                    <p className="font-medium text-gray-700 mb-2">Description</p>
                    <div
                      className="text-gray-600 leading-relaxed prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: book.volumeInfo.description }}
                    />
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  {book.volumeInfo.previewLink && (
                    <Button
                      onClick={() => window.open(book.volumeInfo.previewLink, "_blank")}
                      className="flex-1 bg-blue-600 hover:bg-blue-800"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Preview Book
                    </Button>
                  )}
                  {book.volumeInfo.infoLink && (
                    <Button
                      variant="outline"
                      onClick={() => window.open(book.volumeInfo.infoLink, "_blank")}
                      className="flex-1"
                    >
                      More Info
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  )
}
