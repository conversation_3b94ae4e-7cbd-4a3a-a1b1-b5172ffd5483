"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { FeaturesSection } from "@/components/features-section"
import { NewsletterSection } from "@/components/newsletter-section"
import { DealSection } from "@/components/deal-section"
import { ReviewsSection } from "@/components/reviews-section"
import { BlogsSection } from "@/components/blogs-section"
import { Footer } from "@/components/footer"
import { AuthProvider } from "@/components/auth/auth-provider"
import { AuthGuard } from "@/components/auth/auth-guard"
import { SearchBar } from "@/components/search-bar"
import { BookGrid } from "@/components/book-grid"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { BookOpen, AlertCircle } from "lucide-react"
import type { Review, BlogPost } from "@/types"
import type { GoogleBook } from "@/lib/google-books"
import { getFeaturedBooks, getNewArrivals, searchBooks } from "@/lib/google-books"

export default function HomePage() {
  const [featuredBooks, setFeaturedBooks] = useState<GoogleBook[]>([])
  const [newArrivals, setNewArrivals] = useState<GoogleBook[]>([])
  const [searchResults, setSearchResults] = useState<GoogleBook[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasSearched, setHasSearched] = useState(false)
  const [currentQuery, setCurrentQuery] = useState("")
  const [totalResults, setTotalResults] = useState(0)

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [featured, arrivals] = await Promise.all([getFeaturedBooks(), getNewArrivals()])
        setFeaturedBooks(featured.slice(0, 10))
        setNewArrivals(arrivals.slice(0, 10))
      } catch (err) {
        console.error("Error loading initial data:", err)
      }
    }

    loadInitialData()
  }, [])

  const handleSearch = useCallback(async (query: string, startIndex = 0) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await searchBooks(query, startIndex, 20)

      if (startIndex === 0) {
        setSearchResults(response.items || [])
        setCurrentQuery(query)
        setTotalResults(response.totalItems)
      } else {
        setSearchResults((prev) => [...prev, ...(response.items || [])])
      }

      setHasSearched(true)
    } catch (err) {
      setError("Failed to search books. Please try again.")
      console.error("Search error:", err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const loadMoreResults = () => {
    if (currentQuery && !isLoading) {
      handleSearch(currentQuery, searchResults.length)
    }
  }

  // Sample data for reviews and blogs (keeping original structure)
  const reviews: Review[] = [
    {
      id: "1",
      name: "John Deo",
      rating: 4.5,
      comment:
        "Amazing collection of books! The search functionality works perfectly and I found exactly what I was looking for.",
      image: "/placeholder.svg?height=70&width=70&text=User+1",
    },
    {
      id: "2",
      name: "Sarah Johnson",
      rating: 5,
      comment: "Love this platform! Easy to use and great book recommendations. The preview feature is really helpful.",
      image: "/placeholder.svg?height=70&width=70&text=User+2",
    },
    {
      id: "3",
      name: "Mike Wilson",
      rating: 4.5,
      comment: "Excellent book finder with detailed information about each book. Highly recommend for book lovers!",
      image: "/placeholder.svg?height=70&width=70&text=User+3",
    },
  ]

  const blogs: BlogPost[] = [
    {
      id: "1",
      title: "Top 10 Must-Read Books of 2024",
      excerpt:
        "Discover the most captivating books that are defining literature this year, from thrilling mysteries to heartwarming romances.",
      image: "/placeholder.svg?height=250&width=320&text=Blog+1",
      slug: "top-10-books-2024",
    },
    {
      id: "2",
      title: "How to Build Your Personal Library",
      excerpt:
        "Essential tips for curating a meaningful book collection that reflects your interests and grows with you over time.",
      image: "/placeholder.svg?height=250&width=320&text=Blog+2",
      slug: "build-personal-library",
    },
    {
      id: "3",
      title: "The Rise of Digital Reading",
      excerpt: "Exploring how e-books and audiobooks are changing the way we consume literature in the modern age.",
      image: "/placeholder.svg?height=250&width=320&text=Blog+3",
      slug: "digital-reading-trends",
    },
  ]

  return (
    <AuthProvider>
      <AuthGuard>
        <div className="min-h-screen pb-16 md:pb-0">
          <Header onLoginClick={() => {}} />

          <main>
            {/* Hero Section with Search */}
            <section id="home" className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center">
              <div className="container mx-auto px-4 sm:px-8 py-16">
                <div className="text-center mb-12">
                  <div className="flex items-center justify-center gap-3 mb-6">
                    <BookOpen className="h-12 w-12 text-blue-600" />
                    <h1 className="text-4xl lg:text-6xl font-bold text-gray-800">Book Hunt</h1>
                  </div>
                  <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                    Discover your next great read from millions of books. Search, explore, and find the perfect book for
                    every moment.
                  </p>
                  <SearchBar onSearch={handleSearch} isLoading={isLoading} />
                </div>

                {error && (
                  <Alert className="mb-6 max-w-2xl mx-auto" variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {hasSearched && (
                  <div className="mt-12">
                    <div className="mb-6 text-center">
                      <p className="text-gray-600 text-lg">
                        {totalResults > 0
                          ? `Found ${totalResults.toLocaleString()} results for "${currentQuery}"`
                          : `No results found for "${currentQuery}"`}
                      </p>
                    </div>
                    <BookGrid
                      books={searchResults}
                      onLoadMore={loadMoreResults}
                      hasMore={searchResults.length < totalResults}
                      isLoading={isLoading}
                    />
                  </div>
                )}
              </div>
            </section>

            <FeaturesSection />

            {/* Featured Books Section */}
            {featuredBooks.length > 0 && (
              <section id="featured" className="py-16 px-4 sm:px-8 bg-white">
                <div className="container mx-auto">
                  <div className="relative text-center mb-12">
                    <div className="absolute top-1/2 left-0 right-0 h-px bg-gray-200 -translate-y-1/2 -z-10"></div>
                    <h2 className="inline-block text-3xl font-bold text-gray-800 bg-white px-8 py-4 border border-gray-300">
                      Featured Books
                    </h2>
                  </div>
                  <BookGrid books={featuredBooks.slice(0, 8)} />
                </div>
              </section>
            )}

            <NewsletterSection />

            {/* New Arrivals Section */}
            {newArrivals.length > 0 && (
              <section id="arrivals" className="py-16 px-4 sm:px-8 bg-gray-50">
                <div className="container mx-auto">
                  <div className="relative text-center mb-12">
                    <div className="absolute top-1/2 left-0 right-0 h-px bg-gray-200 -translate-y-1/2 -z-10"></div>
                    <h2 className="inline-block text-3xl font-bold text-gray-800 bg-gray-50 px-8 py-4 border border-gray-300">
                      New Arrivals
                    </h2>
                  </div>
                  <BookGrid books={newArrivals.slice(0, 8)} />
                </div>
              </section>
            )}

            <DealSection />
            <ReviewsSection reviews={reviews} />
            <BlogsSection blogs={blogs} />
          </main>

          <Footer />
        </div>
      </AuthGuard>
    </AuthProvider>
  )
}
