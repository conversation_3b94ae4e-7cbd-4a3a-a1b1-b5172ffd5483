"use client"

import type React from "react"
import { useAuth } from "@/hooks/useAuth"
import { AuthModal } from "./auth-modal"
import { Loader } from "../loader"

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <Loader />
  }

  if (!isAuthenticated) {
    return (
      <>
        <Loader />
        <AuthModal />
      </>
    )
  }

  return <>{children}</>
}
