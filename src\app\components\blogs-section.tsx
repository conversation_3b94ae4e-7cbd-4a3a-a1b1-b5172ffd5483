"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import type { BlogPost } from "@/types"

interface BlogsSectionProps {
  blogs: BlogPost[]
}

export function BlogsSection({ blogs }: BlogsSectionProps) {
  const sliderRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % blogs.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [blogs.length])

  useEffect(() => {
    if (sliderRef.current) {
      const slideWidth = 350 // Width of each blog card including gap
      sliderRef.current.style.transform = `translateX(-${currentIndex * slideWidth}px)`
    }
  }, [currentIndex])

  return (
    <section id="blogs" className="py-16 px-4 sm:px-8">
      <div className="container mx-auto">
        {/* Section Heading */}
        <div className="relative text-center mb-12">
          <div className="absolute top-1/2 left-0 right-0 h-px bg-gray-200 -translate-y-1/2 -z-10"></div>
          <h2 className="inline-block text-3xl font-bold text-gray-800 bg-white px-8 py-4 border border-gray-300">
            Our Blogs
          </h2>
        </div>

        {/* Blogs Slider */}
        <div className="overflow-hidden">
          <div
            ref={sliderRef}
            className="flex transition-transform duration-500 ease-in-out gap-6"
            style={{ width: `${blogs.length * 350}px` }}
          >
            {blogs.map((blog) => (
              <div key={blog.id} className="flex-shrink-0 w-80">
                <div className="bg-white border border-gray-200 hover:border-gray-400 transition-colors duration-300 overflow-hidden">
                  <div className="h-64 overflow-hidden">
                    <Image
                      src={blog.image || "/placeholder.svg"}
                      alt={blog.title}
                      width={320}
                      height={250}
                      className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{blog.title}</h3>
                    <p className="text-gray-600 leading-relaxed mb-4">{blog.excerpt}</p>
                    <Button
                      variant="outline"
                      className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white bg-transparent"
                    >
                      Read More
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
