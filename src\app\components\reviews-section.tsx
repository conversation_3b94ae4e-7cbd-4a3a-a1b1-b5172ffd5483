"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import type { Review } from "@/types"

interface ReviewsSectionProps {
  reviews: Review[]
}

export function ReviewsSection({ reviews }: ReviewsSectionProps) {
  const sliderRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % reviews.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [reviews.length])

  useEffect(() => {
    if (sliderRef.current) {
      const slideWidth = 320 // Width of each review card including gap
      sliderRef.current.style.transform = `translateX(-${currentIndex * slideWidth}px)`
    }
  }, [currentIndex])

  return (
    <section id="reviews" className="py-16 px-4 sm:px-8">
      <div className="container mx-auto">
        {/* Section Heading */}
        <div className="relative text-center mb-12">
          <div className="absolute top-1/2 left-0 right-0 h-px bg-gray-200 -translate-y-1/2 -z-10"></div>
          <h2 className="inline-block text-3xl font-bold text-gray-800 bg-white px-8 py-4 border border-gray-300">
            Client's Reviews
          </h2>
        </div>

        {/* Reviews Slider */}
        <div className="overflow-hidden">
          <div
            ref={sliderRef}
            className="flex transition-transform duration-500 ease-in-out gap-8"
            style={{ width: `${reviews.length * 320}px` }}
          >
            {reviews.map((review) => (
              <div key={review.id} className="flex-shrink-0 w-80">
                <div className="bg-white border border-gray-200 hover:border-gray-400 transition-colors duration-300 p-8 text-center">
                  <Image
                    src={review.image || "/placeholder.svg"}
                    alt={review.name}
                    width={70}
                    height={70}
                    className="w-16 h-16 rounded-full object-cover mx-auto mb-4"
                  />
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">{review.name}</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">{review.comment}</p>
                  <div className="flex justify-center">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className={`text-yellow-400 ${i < review.rating ? "fas" : "far"} fa-star`}>
                        ★
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
